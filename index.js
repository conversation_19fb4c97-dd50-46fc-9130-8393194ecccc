const puppeteer = require('puppeteer');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const puppeteerExtra = require('puppeteer-extra');
const fs = require('fs');
const path = require('path');
const TokenHandler = require('./token.js');
const CaptchaHandler = require('./captcha.js');
const ProxyHandler = require('./proxy.js');
const EmailHandler = require('./email.js');

puppeteerExtra.use(StealthPlugin());

class AutoRegister {
    constructor() {
        this.browser = null;
        this.page = null;
        this.authToken = null;
        this.baseUrl = 'http://127.0.0.1:3000（augment2api运行的ip加端口，nat机填本地IP';
        this.tempEmail = null;
        this.stepCounter = 0;
        this.currentProxy = null;

        this.tokenHandler = new TokenHandler();
        this.captchaHandler = new CaptchaHandler();
        this.proxyHandler = new ProxyHandler();
        this.emailHandler = new EmailHandler();

        this.imageDir = path.join(__dirname, 'image');
        if (!fs.existsSync(this.imageDir)) {
            fs.mkdirSync(this.imageDir, { recursive: true });
        }
    }

    log(message) {
        console.log(`[${new Date().toLocaleTimeString()}] ${message}`);
    }

    async takeScreenshot(stepName, isError = false) {
        try {
            this.stepCounter++;
            const prefix = isError ? 'ERROR' : 'STEP';
            const filename = `${prefix}_${this.stepCounter.toString().padStart(2, '0')}_${stepName}.png`;
            const filepath = path.join(this.imageDir, filename);
            await this.page.screenshot({ path: filepath, fullPage: true });
            this.log(`📸 ${filename}`);
        } catch (error) { }
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async detectCaptcha() {
        try {
            const captchaInfo = await this.page.evaluate(() => {
                const result = {
                    hasTurnstile: false,
                    hasRecaptcha: false,
                    siteKey: null,
                    currentUrl: window.location.href
                };

                const turnstileIframes = document.querySelectorAll('iframe[src*="challenges.cloudflare.com"]');
                if (turnstileIframes.length > 0) {
                    result.hasTurnstile = true;
                    const src = turnstileIframes[0].src;
                    const match = src.match(/0x4[A-Za-z0-9]{20,}/);
                    if (match) result.siteKey = match[0];
                }

                const siteKeyElements = document.querySelectorAll('[data-sitekey]');
                if (siteKeyElements.length > 0) {
                    const sitekey = siteKeyElements[0].getAttribute('data-sitekey');
                    if (sitekey && sitekey.startsWith('0x4')) {
                        result.hasTurnstile = true;
                        result.siteKey = sitekey;
                    }
                }

                if (window.grecaptcha || document.querySelector('.g-recaptcha') ||
                    document.querySelector('[data-sitekey*="6L"]')) {
                    result.hasRecaptcha = true;
                }

                if (!result.siteKey) {
                    const pageContent = document.documentElement.outerHTML;
                    const match = pageContent.match(/0x4[A-Za-z0-9]{20,}/);
                    if (match) {
                        result.siteKey = match[0];
                        result.hasTurnstile = true;
                    }
                }

                return result;
            });

            return captchaInfo;
        } catch (error) {
            return { hasTurnstile: false, hasRecaptcha: false, siteKey: null, currentUrl: this.page.url() };
        }
    }

    async handleCaptchaIfPresent() {
        const captchaInfo = await this.detectCaptcha();

        if (!captchaInfo.hasTurnstile && !captchaInfo.hasRecaptcha) {
            return true;
        }

        this.log('检测到验证码');
        await this.takeScreenshot('captcha_detected');

        let success = false;

        if (captchaInfo.hasTurnstile && captchaInfo.siteKey) {
            success = await this.captchaHandler.handleTurnstile(this.page, captchaInfo.currentUrl, captchaInfo.siteKey);
        }

        if (captchaInfo.hasRecaptcha) {
            const recaptchaSuccess = await this.captchaHandler.handleRecaptchaEnterprise(this.page);
            success = success || recaptchaSuccess;
        }

        if (success) {
            await this.takeScreenshot('captcha_solved');
        } else {
            await this.takeScreenshot('captcha_failed', true);
        }

        return success;
    }

    async initBrowser() {
        try {
            // this.currentProxy = await this.proxyHandler.getValidProxy();
            // this.log(`代理: ${this.currentProxy}`);
        } catch (error) {
            this.log('无代理运行');
            this.currentProxy = null;
        }

        const launchOptions = {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--headless=new',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',
                '--ignore-certificate-errors',
                '--disable-blink-features=AutomationControlled'
            ],
            defaultViewport: { width: 1366, height: 768 },
            timeout: 120000
        };

        // if (this.currentProxy) {
        //     launchOptions.args.push(`--proxy-server=http://${this.currentProxy}`);
        // }

        this.browser = await puppeteerExtra.launch(launchOptions);
        this.page = await this.browser.newPage();
        this.page.setDefaultTimeout(120000);
        this.page.setDefaultNavigationTimeout(120000);

        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        await this.page.evaluateOnNewDocument(() => {
            Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
            window.chrome = { runtime: {} };
            Object.defineProperty(navigator, 'languages', { get: () => ['en-US', 'en'] });
            Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3, 4, 5] });
        });

        this.log('浏览器启动');
        return this.page;
    }

    async findInputField(selectors) {
        for (const selector of selectors) {
            try {
                await this.page.waitForSelector(selector, { timeout: 5000 });
                const input = await this.page.$(selector);
                if (input) return input;
            } catch (e) { continue; }
        }
        return null;
    }

    async clickButtonContaining(keywords, timeout = 15000) {
        try {
            await this.page.waitForSelector('button, a, input[type="submit"], [role="button"]', { timeout });
            const elements = await this.page.$$('button, a, input[type="submit"], [role="button"]');

            for (const element of elements) {
                const text = await this.page.evaluate(el => {
                    return (el.textContent || el.value || el.getAttribute('aria-label') || '').trim();
                }, element);

                for (const keyword of keywords) {
                    if (text.toLowerCase().includes(keyword.toLowerCase())) {
                        await element.click();
                        await this.wait(1000);
                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    async register() {
        try {
            await this.initBrowser();
            await this.takeScreenshot('browser_started');

            //
            this.authToken = await this.tokenHandler.getAuthToken(this.page, this.baseUrl);
            await this.takeScreenshot('auth_token_obtained');

            // 生成临时邮箱
            this.tempEmail = await this.emailHandler.generateTempEmail();
            this.log(`邮箱: ${this.tempEmail}`);

            const authUrl = await this.tokenHandler.getAuthUrl(this.baseUrl, this.authToken);
            this.log(`访问页面B: ${authUrl}`);
            await this.page.goto(authUrl, { waitUntil: 'domcontentloaded', timeout: 120000 });
            await this.wait(5000);
            await this.takeScreenshot('page_B_loaded');

            await this.handleCaptchaIfPresent();

            this.log('填入邮箱');
            const emailSelectors = ['input[type="email"]', 'input[name="email"]', 'input[name="username"]', 'input[id="username"]'];
            const emailInput = await this.findInputField(emailSelectors);
            if (!emailInput) {
                await this.takeScreenshot('no_email_input_B', true);
                throw new Error('页面B未找到邮箱输入框');
            }

            await emailInput.click();
            await this.wait(500);
            await emailInput.type(this.tempEmail, { delay: 100 });
            await this.takeScreenshot('email_entered_B');

            this.log('提交邮箱');
            const emailSubmitted = await this.clickButtonContaining(['Continue', 'Next', '继续']);
            if (!emailSubmitted) await this.page.keyboard.press('Enter');

            await this.wait(8000);
            await this.takeScreenshot('page_C_loaded');
            this.log(`页面C: ${this.page.url()}`);

            await this.handleCaptchaIfPresent();

            // 等待验证码 - email.js内部会自动处理邮件列表检查和验证码提取
            this.log('等待验证码...');
            const verificationCode = await this.emailHandler.waitForVerificationCode();

            this.log('填入验证码');
            const codeSelectors = ['input[name="code"]', 'input[placeholder*="code" i]', 'input[type="text"]:not([readonly])', 'input[autocomplete="one-time-code"]'];
            const codeInput = await this.findInputField(codeSelectors);
            if (!codeInput) {
                await this.takeScreenshot('no_code_input_C', true);
                throw new Error('页面C未找到验证码输入框');
            }

            await codeInput.click();
            await this.wait(500);
            await codeInput.type(verificationCode, { delay: 100 });
            await this.takeScreenshot('code_entered_C');

            await this.handleCaptchaIfPresent();

            this.log('提交验证码');
            const codeSubmitted = await this.clickButtonContaining(['Continue', 'Next', '继续', 'Verify']);
            if (!codeSubmitted) await this.page.keyboard.press('Enter');
            await this.wait(10000);
            await this.takeScreenshot('code_submitted_C');

            const pageContent = await this.page.content();
            if (pageContent.includes('error') || pageContent.includes('failed') || pageContent.includes('blocked')) {
                await this.takeScreenshot('registration_blocked', true);
                await this.handleCaptchaIfPresent();
                await this.takeScreenshot('retry_captcha');
            }

            try {
                const checkboxes = await this.page.$$('input[type="checkbox"], [role="checkbox"]');
                for (const checkbox of checkboxes) {
                    const isChecked = await this.page.evaluate(el =>
                        el.checked || el.getAttribute('aria-checked') === 'true', checkbox);
                    if (!isChecked) {
                        await checkbox.click();
                        await this.takeScreenshot('checkbox_checked');
                        break;
                    }
                }
            } catch (e) { }

            this.log('点击注册');
            const signupClicked = await this.clickButtonContaining(['sign up', 'register', '注册', 'create account']);
            if (!signupClicked) {
                await this.takeScreenshot('no_signup_button', true);
                throw new Error('未找到注册按钮');
            }
            await this.wait(15000);
            await this.takeScreenshot('signup_clicked');

            const jsonData = await this.getJsonData();
            if (jsonData) {
                // await this.tokenHandler.submitCallback(this.baseUrl, this.authToken, jsonData);
                console.log('成功获取到注册数据:');
                console.log(JSON.stringify(jsonData, null, 2));
                await this.takeScreenshot('data_logged');
            }

            await this.cleanup();

            const result = {
                email: this.tempEmail,
                proxy: this.currentProxy,
                status: 'success',
                timestamp: new Date().toISOString()
            };

            this.log(`注册完成: ${this.tempEmail}`);
            return result;

        } catch (error) {
            await this.takeScreenshot('registration_error', true);
            this.log(`注册失败: ${error.message}`);
            await this.cleanup();
            throw error;
        }
    }

    async getJsonData() {
        try {
            await this.page.evaluate(() => {
                window.clipboardData = '';
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    const originalWriteText = navigator.clipboard.writeText;
                    navigator.clipboard.writeText = function (text) {
                        window.clipboardData = text;
                        return originalWriteText.call(this, text);
                    };
                }
            });

            const copyClicked = await this.clickButtonContaining(['copy', 'cop', '复制']);
            if (copyClicked) {
                await this.wait(2000);
                const clipboardData = await this.page.evaluate(() => window.clipboardData);
                if (clipboardData) {
                    return JSON.parse(clipboardData);
                }
            }

            const jsonData = await this.page.evaluate(() => {
                const scripts = Array.from(document.getElementsByTagName('script'));
                for (const script of scripts) {
                    if (script.textContent.includes('let data = {')) {
                        const match = script.textContent.match(/let data = ({[\s\S]*?});/);
                        if (match && match[1]) {
                            return JSON.parse(match[1]);
                        }
                    }
                }
                return null;
            });

            return jsonData;
        } catch (error) {
            return null;
        }
    }

    async cleanup() {
        if (this.emailHandler) this.emailHandler.clearCurrentEmail();
        if (this.proxyHandler) this.proxyHandler.clearProxy();
        if (this.browser) {
            try {
                await this.browser.close();
            } catch (error) { }
            this.browser = null;
            this.page = null;
        }
    }

    async signInAndSolveCaptcha(url) {
        try {
            await this.initBrowser();
            await this.takeScreenshot('browser_started');

            this.log(`Navigating to ${url}`);
            await this.page.goto(url, { waitUntil: 'networkidle2', timeout: 120000 });
            await this.takeScreenshot('page_loaded');

            this.log('Looking for "Sign In" button');
            const signInClicked = await this.clickButtonContaining(['sign in']);
            if (signInClicked) {
                this.log('Clicked "Sign In" button.');
                await this.wait(5000); // Wait for page transition
                await this.takeScreenshot('sign_in_clicked');
            } else {
                this.log('Could not find "Sign In" button.');
                await this.takeScreenshot('no_sign_in_button', true);
            }

            this.log('Checking for CAPTCHA...');
            const captchaSolved = await this.handleCaptchaIfPresent();

            if (captchaSolved) {
                this.log('CAPTCHA handled successfully.');
            } else {
                this.log('No CAPTCHA found or failed to handle.');
            }

            this.log('Process finished.');

        } catch (error) {
            await this.takeScreenshot('process_error', true);
            this.log(`An error occurred: ${error.message}`);
            throw error;
        } finally {
            await this.cleanup();
        }
    }
}

async function main() {
    const autoRegister = new AutoRegister();

    try {
        console.log('🚀 Starting sign-in process');
        await autoRegister.signInAndSolveCaptcha('https://www.augmentcode.com/');
        console.log('🎉 Process completed successfully.');

    } catch (error) {
        console.error('💥 Process failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = AutoRegister;

