{"version": 3, "file": "NavigationTracker.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/context/NavigationTracker.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;GAgBG;;;AAIH,+DAGuC;AACvC,4DAAoD;AACpD,kDAA6D;AAC7D,oDAAoD;AACpD,gEAAkE;AAClE,oDAA8C;AAU9C,MAAa,gBAAgB;IAClB,SAAS,CAAsB;IAC/B,OAAO,CAAU;IAE1B,YAAY,SAA8B,EAAE,OAAgB;QAC1D,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;CACF;AARD,4CAQC;AAED,MAAa,eAAe;IACjB,YAAY,GAAG,IAAA,gBAAM,GAAE,CAAC;IACxB,kBAAkB,CAAS;IAEpC,QAAQ,GAAG,KAAK,CAAC;IACjB,SAAS,GAAG,IAAI,sBAAQ,EAAoB,CAAC;IAC7C,GAAG,CAAS;IACZ,QAAQ,CAAU;IAClB,UAAU,CAAU;IACpB,aAAa,CAAe;IAC5B,SAAS,GAAG,IAAI,sBAAQ,EAAQ,CAAC;IACjC,oBAAoB,CAAW;IAE/B,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,YACE,GAAW,EACX,iBAAyB,EACzB,SAAkB,EAClB,YAA0B;QAE1B,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;IACpC,CAAC;IAED,cAAc;QACZ,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,kBAAkB;YAChC,UAAU,EAAE,IAAI,CAAC,YAAY;YAC7B,SAAS,EAAE,IAAA,sBAAY,GAAE;YACzB,GAAG,EAAE,IAAI,CAAC,GAAG;SACd,CAAC;IACJ,CAAC;IAED,KAAK;QACH;QACE,6CAA6C;QAC7C,CAAC,IAAI,CAAC,UAAU;YAChB,iDAAiD;YACjD,CAAC,IAAI,CAAC,QAAQ;YACd,8EAA8E;YAC9E,uFAAuF;YACvF,CAAC,IAAI,CAAC,oBAAoB,EAC1B,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,aAAa,CAC9B;gBACE,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,0BAAY,CAAC,eAAe,CAAC,UAAU,CAAC,iBAAiB;gBACjE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE;aAC9B,EACD,IAAI,CAAC,kBAAkB,CACxB,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,OAAO,CAAC,gBAAkC;QACxC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IACE,CAAC,IAAI,CAAC,UAAU;YAChB,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU;YAC1B,gBAAgB,CAAC,SAAS,0DAA6B,EACvD,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,aAAa,CAC9B;gBACE,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,gBAAgB,CAAC,SAAS;gBAClC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE;aAC9B,EACD,IAAI,CAAC,kBAAkB,CACxB,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC3C,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACzB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,aAAa,CAC9B;gBACE,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,0BAAY,CAAC,eAAe,CAAC,UAAU,CAAC,mBAAmB;gBACnE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE;aAC9B,EACD,IAAI,CAAC,kBAAkB,CACxB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACzB,IAAI,CAAC,OAAO,CAAC,IAAI,gBAAgB,iFAAuC,CAAC,CAAC;IAC5E,CAAC;IAED,IAAI;QACF,IAAI,CAAC,OAAO,CAAC,IAAI,gBAAgB,uDAA0B,CAAC,CAAC;IAC/D,CAAC;IAED,IAAI,CAAC,OAAe;QAClB,IAAI,CAAC,OAAO,CACV,IAAI,gBAAgB,CAClB,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,CAAC;YACD,CAAC,8EAAqC,EACxC,OAAO,CACR,CACF,CAAC;IACJ,CAAC;CACF;AAlHD,0CAkHC;AAED;;GAEG;AACH,MAAa,iBAAiB;IACnB,aAAa,CAAe;IAC5B,OAAO,CAAY;IACnB,yBAAyB,GAAG,IAAI,GAAG,EAA2B,CAAC;IAE/D,kBAAkB,CAAS;IACpC;;;OAGG;IACH,wBAAwB,CAAkB;IAC1C;;OAEG;IACH,kBAAkB,CAAmB;IAErC,mEAAmE;IACnE,oBAAoB,GAAG,IAAI,CAAC;IAE5B,YACE,GAAW,EACX,iBAAyB,EACzB,YAA0B,EAC1B,MAAiB;QAEjB,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,8CAA8C;QAC9C,IAAI,CAAC,wBAAwB,GAAG,IAAI,eAAe,CACjD,GAAG,EACH,iBAAiB,EACjB,IAAA,oCAAoB,EAAC,GAAG,CAAC,EACzB,IAAI,CAAC,aAAa,CACnB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,IAAI,mBAAmB;QACrB,IAAI,IAAI,CAAC,kBAAkB,EAAE,oBAAoB,KAAK,KAAK,EAAE,CAAC;YAC5D,+EAA+E;YAC/E,OAAO,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC;QAC9C,CAAC;QAED,gFAAgF;QAChF,uCAAuC;QACvC,OAAO,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACH,uBAAuB,CACrB,GAAW,EACX,yBAAkC,KAAK;QAEvC,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,KAAK,EAAE,yBAAyB,CAAC,CAAC;QACzD,IAAI,CAAC,oBAAoB;YACvB,sBAAsB;gBACtB,IAAI,CAAC,oBAAoB;gBACzB,IAAA,oCAAoB,EAAC,GAAG,CAAC,CAAC;QAE5B,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAC3B,8CAA8C,CAC/C,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,eAAe,CACpC,GAAG,EACH,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,aAAa,CACnB,CAAC;QACF,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACrC,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,OAAO;QACL,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACzE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAChC,yCAAyC,CAC1C,CAAC;IACJ,CAAC;IAED,0BAA0B;IAC1B,mBAAmB,CAAC,GAAW;QAC7B,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,KAAK,EAAE,uBAAuB,GAAG,EAAE,CAAC,CAAC;QAC5D,IAAI,CAAC,wBAAwB,CAAC,GAAG,GAAG,GAAG,CAAC;IAC1C,CAAC;IAED,+BAA+B,CAC7B,GAAW,EACX,QAAgB;QAEhB,IAAI,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;QACvD,CAAC;QAED,IACE,IAAI,CAAC,kBAAkB,KAAK,SAAS;YACrC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,KAAK,SAAS,EAC9C,CAAC;YACD,kFAAkF;YAClF,mCAAmC;YACnC,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACjC,CAAC;QACD,mCAAmC;QACnC,OAAO,IAAI,CAAC,uBAAuB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,GAAW,EAAE,QAAgB,EAAE,cAAuB;QACnE,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,KAAK,EAAE,kBAAkB,GAAG,EAAE,CAAC,CAAC;QAEvD,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YACjC,yBAAyB;YACzB,MAAM,UAAU,GACd,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAC;gBAC5C,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;YACrD,UAAU,CAAC,GAAG,GAAG,cAAc,CAAC;YAChC,UAAU,CAAC,KAAK,EAAE,CAAC;YACnB,UAAU,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YACpD,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,+BAA+B,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QAEvE,IAAI,UAAU,KAAK,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACjD,kFAAkF;YAClF,2CAA2C;YAC3C,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAChC,8CAA8C,CAC/C,CAAC;QACJ,CAAC;QAED,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;QACrB,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC/B,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACzD,UAAU,CAAC,KAAK,EAAE,CAAC;QACnB,UAAU,CAAC,cAAc,EAAE,CAAC;QAE5B,IAAI,CAAC,wBAAwB,GAAG,UAAU,CAAC;QAC3C,IAAI,IAAI,CAAC,kBAAkB,KAAK,UAAU,EAAE,CAAC;YAC3C,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;QACtC,CAAC;IACH,CAAC;IAED,uBAAuB,CACrB,GAAW,EACX,cAA4E;QAE5E,IAAI,CAAC,OAAO,EAAE,CACZ,gBAAO,CAAC,KAAK,EACb,2BAA2B,GAAG,KAAK,cAAc,EAAE,CACpD,CAAC;QAEF,4CAA4C;QAC5C,IAAI,CAAC,wBAAwB,CAAC,GAAG,GAAG,GAAG,CAAC;QAExC,IAAI,cAAc,KAAK,UAAU,EAAE,CAAC;YAClC,6DAA6D;YAC7D,OAAO;QACT,CAAC;QAED,sFAAsF;QACtF,yDAAyD;QACzD,MAAM,kBAAkB,GACtB,IAAI,CAAC,kBAAkB,EAAE,oBAAoB,KAAK,IAAI;YACpD,CAAC,CAAC,IAAI,CAAC,kBAAkB;YACzB,CAAC,CAAC,IAAI,eAAe,CACjB,GAAG,EACH,IAAI,CAAC,kBAAkB,EACvB,KAAK,EACL,IAAI,CAAC,aAAa,CACnB,CAAC;QAER,6BAA6B;QAC7B,kBAAkB,CAAC,iBAAiB,EAAE,CAAC;QAEvC,IAAI,kBAAkB,KAAK,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACnD,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,aAAa,CAAC,QAAgB;QAC5B,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;QAC/C,wDAAwD;QACxD,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAElC,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,UAA2B,EAAE,SAAiB;QAC3D,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,KAAK,EAAE,uBAAuB,CAAC,CAAC;QACvD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACH,yBAAyB,CAAC,UAA2B,EAAE,QAAiB;QACtE,IAAI,CAAC,OAAO,EAAE,CACZ,gBAAO,CAAC,KAAK,EACb,2BAA2B,UAAU,CAAC,YAAY,KAAK,QAAQ,EAAE,CAClE,CAAC;QAEF,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAC/B,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC3D,CAAC;QAED,UAAU,CAAC,oBAAoB,GAAG,QAAQ,KAAK,SAAS,CAAC;IAC3D,CAAC;IAED,sBAAsB,CACpB,GAAW,EACX,QAAgB,EAChB,cAAsB;QAEtB,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,KAAK,EAAE,0BAA0B,GAAG,KAAK,QAAQ,EAAE,CAAC,CAAC;QAE5E,IACE,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,kBAAkB,EAAE,QAAQ,KAAK,SAAS;YAC/C,IAAI,CAAC,kBAAkB,EAAE,QAAQ,KAAK,QAAQ,EAC9C,CAAC;YACD,gFAAgF;YAChF,wCAAwC;YACxC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAC3B,8CAA8C,CAC/C,CAAC;YACF,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;QACtC,CAAC;QAED,IAAI,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjD,MAAM,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;YACzE,wEAAwE;YACxE,kBAAkB,CAAC,oBAAoB;gBACrC,iBAAiB,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;YAC1D,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;YAC7C,OAAO;QACT,CAAC;QAED,MAAM,iBAAiB,GACrB,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,uBAAuB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAErE,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QAEhE,iBAAiB,CAAC,oBAAoB;YACpC,iBAAiB,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;QAE1D,iBAAiB,CAAC,GAAG,GAAG,GAAG,CAAC;QAC5B,iBAAiB,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACtC,iBAAiB,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;IAED,MAAM,CAAC,qBAAqB,CAAC,cAAsB;QACjD,iFAAiF;QACjF,+EAA+E;QAC/E,6DAA6D;QAC7D,4FAA4F;QAC5F,OAAO,CAAC,qBAAqB,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;IAC1E,CAAC;IACD;;;OAGG;IACH,oBAAoB,CAAC,QAAgB,EAAE,SAAiB;QACtD,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAChE,CAAC;CACF;AA9SD,8CA8SC"}