{"version": 3, "file": "LogManager.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/log/LogManager.ts"], "names": [], "mappings": ";;;;AAkBA,+DAAwE;AACxE,kDAA6D;AAM7D,iDAAmD;AAEnD,gEAAgE;AAChE,SAAS,iBAAiB,CACxB,aAAsD;IAEtD,MAAM,WAAW,GAAG,aAAa,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;QAC9D,OAAO;YACL,YAAY,EAAE,SAAS,CAAC,YAAY;YACpC,YAAY,EAAE,SAAS,CAAC,YAAY;YACpC,UAAU,EAAE,SAAS,CAAC,UAAU;YAChC,GAAG,EAAE,SAAS,CAAC,GAAG;SACnB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,OAAO,WAAW,CAAC,CAAC,CAAC,EAAC,UAAU,EAAE,WAAW,EAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAC7D,CAAC;AAED,SAAS,WAAW,CAAC,cAAsB;IACzC,IAAI,gCAAkB,QAAQ,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;QACzD,qCAAuB;IACzB,CAAC;IACD,IAAI,gCAAkB,OAAO,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;QACxD,qCAAuB;IACzB,CAAC;IACD,IAAI,8BAAiB,SAAS,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;QACzD,mCAAsB;IACxB,CAAC;IACD,mCAAsB;AACxB,CAAC;AAED,SAAS,YAAY,CAAC,cAAsB;IAC1C,QAAQ,cAAc,EAAE,CAAC;QACvB,KAAK,SAAS;YACZ,OAAO,MAAM,CAAC;QAChB,KAAK,YAAY;YACf,OAAO,OAAO,CAAC;QACjB,KAAK,qBAAqB;YACxB,OAAO,gBAAgB,CAAC;QAC1B,KAAK,UAAU;YACb,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,MAAa,UAAU;IACZ,aAAa,CAAe;IAC5B,aAAa,CAAe;IAC5B,UAAU,CAAY;IACtB,OAAO,CAAY;IAE5B,YACE,SAAoB,EACpB,YAA0B,EAC1B,YAA0B,EAC1B,MAAiB;QAEjB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED,MAAM,CAAC,MAAM,CACX,SAAoB,EACpB,YAA0B,EAC1B,YAA0B,EAC1B,MAAiB;QAEjB,MAAM,UAAU,GAAG,IAAI,EAAU,CAC/B,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,MAAM,CACP,CAAC;QAEF,UAAU,CAAC,kCAAkC,EAAE,CAAC;QAEhD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,sBAAsB,CAC1B,GAAkC,EAClC,KAAY;QAEZ,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;YACjB,wEAAwE;YACxE,YAAY;YACZ,KAAK,WAAW;gBACd,OAAO,EAAC,IAAI,EAAE,WAAW,EAAC,CAAC;YAC7B,KAAK,SAAS;gBACZ,OAAO,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAC,CAAC;YAC7C,KAAK,QAAQ;gBACX,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAC,CAAC;YAC5C,KAAK,QAAQ;gBACX,wEAAwE;gBACxE,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,mBAAmB,IAAI,GAAG,CAAC,KAAK,EAAC,CAAC;YACvE,KAAK,QAAQ;gBACX,IACE,GAAG,CAAC,mBAAmB,KAAK,SAAS;oBACrC,GAAG,CAAC,mBAAmB,CAAC,GAAG,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EACnE,CAAC;oBACD,OAAO;wBACL,IAAI,EAAE,GAAG,CAAC,IAAI;wBACd,KAAK,EAAE,GAAG,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;qBAC5C,CAAC;gBACJ,CAAC;gBACD,gEAAgE;gBAChE,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,GAAG,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;oBAC3B,OAAO,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;gBACxB,CAAC;gBACD,uCAAuC;gBACvC,MAAM;YACR;gBACE,uCAAuC;gBACvC,MAAM;QACV,CAAC;QACD,uCAAuC;QACvC,OAAO,MAAM,KAAK,CAAC,kBAAkB,CAAC,GAAG,2CAA8B,CAAC;IAC1E,CAAC;IAED,kCAAkC;QAChC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,0BAA0B,EAAE,CAAC,MAAM,EAAE,EAAE;YAClE,gEAAgE;YAChE,eAAe;YACf,MAAM,KAAK,GAAsB,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;gBAC5D,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY;gBAC1C,kBAAkB,EAAE,MAAM,CAAC,kBAAkB;aAC9C,CAAC,CAAC;YACH,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACxB,+CAA+C;gBAC/C,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;gBACpC,OAAO;YACT,CAAC;YAED,MAAM,WAAW,GAAkC,OAAO,CAAC,GAAG,CAC5D,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAClE,CAAC;YAEF,KAAK,MAAM,eAAe,IAAI,KAAK,CAAC,0BAA0B,EAAE,CAAC;gBAC/D,IAAI,CAAC,aAAa,CAAC,oBAAoB,CACrC,WAAW,CAAC,IAAI,CACd,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;oBACT,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE;wBACL,IAAI,EAAE,OAAO;wBACb,MAAM,EAAE,0BAAY,CAAC,GAAG,CAAC,UAAU,CAAC,aAAa;wBACjD,MAAM,EAAE;4BACN,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC;4BAC/B,MAAM,EAAE,KAAK,CAAC,MAAM;4BACpB,IAAI,EAAE,IAAA,kCAAmB,EAAC,IAAI,EAAE,IAAI,CAAC;4BACrC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;4BACvC,UAAU,EAAE,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC;4BAChD,IAAI,EAAE,SAAS;4BACf,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC;4BACjC,IAAI;yBACL;qBACF;iBACF,CAAC,EACF,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,KAAK;iBACN,CAAC,CACH,EACD,eAAe,CAAC,EAAE,EAClB,0BAAY,CAAC,GAAG,CAAC,UAAU,CAAC,aAAa,CAC1C,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,yBAAyB,EAAE,CAAC,MAAM,EAAE,EAAE;YACjE,gEAAgE;YAChE,eAAe;YACf,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;gBACzC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY;gBAC1C,kBAAkB,EAAE,MAAM,CAAC,gBAAgB,CAAC,kBAAkB;aAC/D,CAAC,CAAC;YACH,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACxB,+CAA+C;gBAC/C,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;gBACpC,OAAO;YACT,CAAC;YAED,KAAK,MAAM,eAAe,IAAI,KAAK,CAAC,0BAA0B,EAAE,CAAC;gBAC/D,IAAI,CAAC,aAAa,CAAC,oBAAoB,CACrC,EAAU,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,IAAI,CAC9C,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;oBACT,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE;wBACL,IAAI,EAAE,OAAO;wBACb,MAAM,EAAE,0BAAY,CAAC,GAAG,CAAC,UAAU,CAAC,aAAa;wBACjD,MAAM,EAAE;4BACN,KAAK,+BAAiB;4BACtB,MAAM,EAAE,KAAK,CAAC,MAAM;4BACpB,IAAI;4BACJ,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;4BACvC,UAAU,EAAE,iBAAiB,CAC3B,MAAM,CAAC,gBAAgB,CAAC,UAAU,CACnC;4BACD,IAAI,EAAE,YAAY;yBACnB;qBACF;iBACF,CAAC,EACF,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,KAAK;iBACN,CAAC,CACH,EACD,eAAe,CAAC,EAAE,EAClB,0BAAY,CAAC,GAAG,CAAC,UAAU,CAAC,aAAa,CAC1C,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAC5B,MAA6C,EAC7C,KAAa;QAEb,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;YACvC,OAAO,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC;QACtC,CAAC;QACD,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC3D,CAAC;QACD,OAAO,MAAM,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IACxE,CAAC;CACF;AA/LD,gCA+LC"}