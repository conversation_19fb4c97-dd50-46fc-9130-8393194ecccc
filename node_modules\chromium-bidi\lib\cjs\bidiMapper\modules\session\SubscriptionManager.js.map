{"version": 3, "file": "SubscriptionManager.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/session/SubscriptionManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAmBH,4CAIC;AAGD,oCAqCC;AAiUD,gCAQC;AArYD,+DAMuC;AACvC,oDAA8C;AAG9C;;;;;GAKG;AACH,SAAgB,gBAAgB,CAAC,GAAG,CAAU;IAC5C,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAY,EAAE,CAAY,EAAE,EAAE,CAC7C,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAC9C,CAAC;AACJ,CAAC;AAED,qDAAqD;AACrD,SAAgB,YAAY,CAC1B,MAAiC;IAEjC,MAAM,SAAS,GAAG,IAAI,GAAG,EAA2B,CAAC;IAErD,SAAS,SAAS,CAAC,MAAiC;QAClD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAED,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QAC3B,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,0BAAY,CAAC,UAAU,CAAC,SAAS;gBACpC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,0BAAY,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC5D,MAAM;YACR,KAAK,0BAAY,CAAC,UAAU,CAAC,eAAe;gBAC1C,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,0BAAY,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC;gBAClE,MAAM;YACR,KAAK,0BAAY,CAAC,UAAU,CAAC,KAAK;gBAChC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,0BAAY,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;gBACxD,MAAM;YACR,KAAK,0BAAY,CAAC,UAAU,CAAC,GAAG;gBAC9B,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,0BAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;gBACtD,MAAM;YACR,KAAK,0BAAY,CAAC,UAAU,CAAC,OAAO;gBAClC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,0BAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,0BAAY,CAAC,UAAU,CAAC,MAAM;gBACjC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,0BAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;gBACzD,MAAM;YACR;gBACE,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC,MAAM,EAAE,CAAC;AAC5B,CAAC;AAYD,MAAa,mBAAmB;IAC9B,cAAc,GAAmB,EAAE,CAAC;IACpC,qBAAqB,GAAG,IAAI,GAAG,EAAU,CAAC;IAC1C,uBAAuB,CAAyB;IAEhD,YAAY,sBAA8C;QACxD,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;IACxD,CAAC;IAED,gCAAgC,CAC9B,SAAkC,EAClC,SAA0C;QAE1C,MAAM,YAAY,GAAG,IAAI,GAAG,EAAe,CAAC;QAE5C,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/C,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC;gBAC7D,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAClC,CAAC;IAED,wCAAwC,CACtC,SAAkC;QAElC,MAAM,YAAY,GAAG,IAAI,GAAG,EAAe,CAAC;QAE5C,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/C,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,SAAS,CAAC,EAAE,CAAC;gBAClD,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAClC,CAAC;IAED,eAAe,CACb,YAA0B,EAC1B,aAAsC,EACtC,iBAAmD;QAEnD,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,KAAK,MAAM,SAAS,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;YAChD,6CAA6C;YAC7C,kCAAkC;YAClC;YACE,8BAA8B;YAC9B,SAAS,KAAK,aAAa;gBAC3B,8BAA8B;gBAC9B,SAAS,KAAK,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5C,iDAAiD;gBACjD,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,aAAa,EAC5C,CAAC;gBACD,aAAa,GAAG,IAAI,CAAC;gBACrB,MAAM;YACR,CAAC;QACH,CAAC;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,6BAA6B;QAC7B,IAAI,YAAY,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACvB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,OAAO,GACX,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;YAC9D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,YAAY,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC9D,CAAC;QAED,wBAAwB;QACxB,IAAI,YAAY,CAAC,sBAAsB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACvB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,eAAe,GACnB,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;YACxE,OAAO,CACL,eAAe,KAAK,IAAI;gBACxB,YAAY,CAAC,sBAAsB,CAAC,GAAG,CAAC,eAAe,CAAC,CACzD,CAAC;QACJ,CAAC;QAED,uBAAuB;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc,CACZ,aAAsC,EACtC,SAA0C;QAE1C,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/C,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,aAAa,EAAE,SAAS,CAAC,EAAE,CAAC;gBACjE,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;OAMG;IACH,SAAS,CACP,UAAqC,EACrC,UAA6C,EAC7C,cAAqC,EACrC,WAAwB;QAExB,+DAA+D;QAC/D,MAAM,YAAY,GAAiB;YACjC,EAAE,EAAE,IAAA,gBAAM,GAAE;YACZ,UAAU,EAAE,IAAI,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAC7C,sBAAsB,EAAE,IAAI,GAAG,CAC7B,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;gBAC3B,MAAM,eAAe,GACnB,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;gBAChE,IAAI,CAAC,eAAe,EAAE,CAAC;oBACrB,MAAM,IAAI,kCAAoB,CAC5B,gDAAgD,SAAS,EAAE,CAC5D,CAAC;gBACJ,CAAC;gBACD,OAAO,eAAe,CAAC;YACzB,CAAC,CAAC,CACH;YACD,cAAc,EAAE,IAAI,GAAG,CAAC,cAAc,CAAC;YACvC,WAAW;SACZ,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACvC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAChD,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACH,WAAW,CACT,eAA0C,EAC1C,eAAkD,EAClD,WAAwB;QAExB,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC;QAE1D,kCAAkC;QAClC,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;QAEjE,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAClC,eAAe,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;YAChC,MAAM,eAAe,GACnB,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;YAChE,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,MAAM,IAAI,kCAAoB,CAC5B,gDAAgD,SAAS,EAAE,CAC5D,CAAC;YACJ,CAAC;YACD,OAAO,eAAe,CAAC;QACzB,CAAC,CAAC,CACH,CAAC;QAEF,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,IAAI,KAAK,CAAC,CAAC;QAC5D,MAAM,gBAAgB,GAAmB,EAAE,CAAC;QAC5C,MAAM,aAAa,GAAG,IAAI,GAAG,EAA2B,CAAC;QACzD,MAAM,eAAe,GAAG,IAAI,GAAG,EAAmC,CAAC;QACnE,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/C,IAAI,YAAY,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC;gBAC7C,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACpC,SAAS;YACX,CAAC;YACD,mCAAmC;YACnC,IAAI,YAAY,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC3C,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACpC,SAAS;YACX,CAAC;YACD,yDAAyD;YACzD,IAAI,YAAY,CAAC,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBACjE,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACpC,SAAS;YACX,CAAC;YACD,IAAI,mBAAmB,EAAE,CAAC;gBACxB,iCAAiC;gBACjC,IAAI,YAAY,CAAC,sBAAsB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;oBACnD,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBACpC,SAAS;gBACX,CAAC;gBACD,MAAM,sBAAsB,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAChE,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;oBACnC,IAAI,sBAAsB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;wBAC1C,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;wBAC7B,sBAAsB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBAC3C,CAAC;gBACH,CAAC;gBACD,yDAAyD;gBACzD,IAAI,sBAAsB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;oBACtC,gBAAgB,CAAC,IAAI,CAAC;wBACpB,GAAG,YAAY;wBACf,UAAU,EAAE,sBAAsB;qBACnC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,6BAA6B;gBAC7B,IAAI,YAAY,CAAC,sBAAsB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;oBACnD,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBACpC,SAAS;gBACX,CAAC;gBAED,mCAAmC;gBACnC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAGrB,CAAC;gBACJ,KAAK,MAAM,SAAS,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;oBAChD,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,GAAG,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC;gBACxE,CAAC;gBACD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;oBACnC,MAAM,eAAe,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBAChD,IAAI,CAAC,eAAe,EAAE,CAAC;wBACrB,SAAS;oBACX,CAAC;oBACD,KAAK,MAAM,UAAU,IAAI,oBAAoB,EAAE,CAAC;wBAC9C,IAAI,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;4BACpC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;4BAChC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;4BAC7B,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;wBACrC,CAAC;oBACH,CAAC;oBACD,IAAI,eAAe,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;wBAC/B,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBAC7B,CAAC;gBACH,CAAC;gBACD,KAAK,MAAM,CAAC,SAAS,EAAE,mBAAmB,CAAC,IAAI,QAAQ,EAAE,CAAC;oBACxD,MAAM,mBAAmB,GAAiB;wBACxC,EAAE,EAAE,YAAY,CAAC,EAAE;wBACnB,WAAW,EAAE,YAAY,CAAC,WAAW;wBACrC,UAAU,EAAE,IAAI,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;wBAChC,sBAAsB,EAAE,mBAAmB;wBAC3C,cAAc,EAAE,IAAI,GAAG,EAAE;qBAC1B,CAAC;oBACF,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC;QACH,CAAC;QAED,0DAA0D;QAC1D,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,sCAAwB,CAAC,uBAAuB,CAAC,CAAC;QAC9D,CAAC;QAED,4DAA4D;QAC5D,IAAI,CAAC,mBAAmB,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,oBAAoB,CAAC,EAAE,CAAC;YAC1E,MAAM,IAAI,sCAAwB,CAAC,uBAAuB,CAAC,CAAC;QAC9D,CAAC;QAED,oCAAoC;QACpC,IAAI,CAAC,cAAc,GAAG,gBAAgB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,eAAyB;QACvC,MAAM,kBAAkB,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,CAAC;QACpD,MAAM,UAAU,GAAG,UAAU,CAC3B,kBAAkB,EAClB,IAAI,CAAC,qBAAqB,CAC3B,CAAC;QAEF,IAAI,UAAU,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,sCAAwB,CAAC,uBAAuB,CAAC,CAAC;QAC9D,CAAC;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE;YAChE,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,qBAAqB,GAAG,UAAU,CACrC,IAAI,CAAC,qBAAqB,EAC1B,kBAAkB,CACnB,CAAC;IACJ,CAAC;CACF;AAnSD,kDAmSC;AAED;;GAEG;AACH,SAAS,YAAY,CAAI,IAAY,EAAE,IAAY;IACjD,MAAM,MAAM,GAAG,IAAI,GAAG,EAAK,CAAC;IAC5B,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QACrB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAChB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAgB,UAAU,CAAI,IAAY,EAAE,IAAY;IACtD,MAAM,MAAM,GAAG,IAAI,GAAG,EAAK,CAAC;IAC5B,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YACjB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,KAAK,CAAI,IAAY,EAAE,IAAY;IAC1C,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAC5B,OAAO,KAAK,CAAC;IACf,CAAC;IACD,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}