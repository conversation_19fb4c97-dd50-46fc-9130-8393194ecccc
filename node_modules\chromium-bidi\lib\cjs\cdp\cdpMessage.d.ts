/**
 * Copyright 2021 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { Protocol } from 'devtools-protocol';
import type { ProtocolMapping } from 'devtools-protocol/types/protocol-mapping.js';
export interface CdpError {
    code: number;
    message: string;
}
export interface CdpMessage<CdpMethod extends keyof ProtocolMapping.Commands> {
    sessionId?: Protocol.Target.SessionID;
    id?: number;
    error?: CdpError;
    method?: CdpMethod;
    params?: ProtocolMapping.Commands[CdpMethod]['paramsType'][0];
    result?: ProtocolMapping.Commands[CdpMethod]['returnType'];
}
