{"version": 3, "file": "webdriver-bidi-permissions.js", "sourceRoot": "", "sources": ["../../../../src/protocol-parser/generated/webdriver-bidi-permissions.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH;;;;GAIG;AAEH,6DAA6D;AAC7D,0CAA0C;AAE1C,OAAO,CAAC,MAAM,KAAK,CAAC;AAEpB,MAAM,CAAC,MAAM,wBAAwB,GAAG,CAAC,CAAC,IAAI,CAC5C,GAAG,EAAE,CAAC,WAAW,CAAC,mBAAmB,CACtC,CAAC;AACF,MAAM,KAAW,WAAW,CAM3B;AAND,WAAiB,WAAW;IACb,sCAA0B,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CACpD,CAAC,CAAC,MAAM,CAAC;QACP,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;KACjB,CAAC,CACH,CAAC;AACJ,CAAC,EANgB,WAAW,KAAX,WAAW,QAM3B;AACD,WAAiB,WAAW;IACb,iCAAqB,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAC/C,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CACxC,CAAC;AACJ,CAAC,EAJgB,WAAW,KAAX,WAAW,QAI3B;AACD,WAAiB,WAAW;IACb,+BAAmB,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAC7C,CAAC,CAAC,MAAM,CAAC;QACP,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC;QAC9C,MAAM,EAAE,WAAW,CAAC,6BAA6B;KAClD,CAAC,CACH,CAAC;AACJ,CAAC,EAPgB,WAAW,KAAX,WAAW,QAO3B;AACD,WAAiB,WAAW;IACb,yCAA6B,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CACvD,CAAC,CAAC,MAAM,CAAC;QACP,UAAU,EAAE,WAAW,CAAC,0BAA0B;QAClD,KAAK,EAAE,WAAW,CAAC,qBAAqB;QACxC,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE;QAClB,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KACnC,CAAC,CACH,CAAC;AACJ,CAAC,EATgB,WAAW,KAAX,WAAW,QAS3B"}