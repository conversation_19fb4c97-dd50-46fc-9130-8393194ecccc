/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
import type { CdpFrame } from './Frame.js';
/**
 * @internal
 */
export declare class CdpPreloadScript {
    #private;
    constructor(mainFrame: CdpFrame, id: string, source: string);
    get id(): string;
    get source(): string;
    getIdForFrame(frame: CdpFrame): string | undefined;
    setIdForFrame(frame: CdpFrame, identifier: string): void;
}
//# sourceMappingURL=CdpPreloadScript.d.ts.map