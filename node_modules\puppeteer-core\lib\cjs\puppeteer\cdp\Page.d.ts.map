{"version": 3, "file": "Page.d.ts", "sourceRoot": "", "sources": ["../../../../src/cdp/Page.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAGhD,OAAO,KAAK,EAAC,OAAO,EAAC,MAAM,mBAAmB,CAAC;AAC/C,OAAO,KAAK,EAAC,cAAc,EAAC,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAkB,KAAK,UAAU,EAAC,MAAM,sBAAsB,CAAC;AAEtE,OAAO,KAAK,EAAC,KAAK,EAAE,cAAc,EAAC,MAAM,iBAAiB,CAAC;AAC3D,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,wBAAwB,CAAC;AACzD,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,oBAAoB,CAAC;AACjD,OAAO,KAAK,EAAC,WAAW,EAAC,MAAM,gBAAgB,CAAC;AAChD,OAAO,EACL,IAAI,EAEJ,KAAK,kBAAkB,EACvB,KAAK,YAAY,EACjB,KAAK,OAAO,EACZ,KAAK,2BAA2B,EAEhC,KAAK,iBAAiB,EACtB,KAAK,kBAAkB,EACxB,MAAM,gBAAgB,CAAC;AAKxB,OAAO,KAAK,EACV,MAAM,EACN,oBAAoB,EACpB,WAAW,EACX,kBAAkB,EACnB,MAAM,qBAAqB,CAAC;AAG7B,OAAO,EAAC,WAAW,EAAC,MAAM,0BAA0B,CAAC;AAErD,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,yBAAyB,CAAC;AAWxD,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,uBAAuB,CAAC;AAOpD,OAAO,EAAC,aAAa,EAAC,MAAM,iBAAiB,CAAC;AAE9C,OAAO,EAAC,QAAQ,EAAC,MAAM,eAAe,CAAC;AACvC,OAAO,KAAK,EAAC,mBAAmB,EAAC,MAAM,0BAA0B,CAAC;AAGlE,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,YAAY,CAAC;AAGzC,OAAO,EAAC,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAC,MAAM,YAAY,CAAC;AAIjE,OAAO,KAAK,EAAC,iBAAiB,EAAC,MAAM,qBAAqB,CAAC;AAC3D,OAAO,KAAK,EAAC,SAAS,EAAC,MAAM,aAAa,CAAC;AAG3C,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AAMrC,OAAO,EAAC,YAAY,EAAC,MAAM,gBAAgB,CAAC;AAW5C;;GAEG;AACH,qBAAa,OAAQ,SAAQ,IAAI;;WAClB,OAAO,CAClB,MAAM,EAAE,aAAa,EACrB,MAAM,EAAE,SAAS,EACjB,eAAe,EAAE,QAAQ,GAAG,IAAI,GAC/B,OAAO,CAAC,OAAO,CAAC;gBAwCP,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS;IAsPpD,OAAO,IAAI,UAAU;IAIZ,uBAAuB,IAAI,OAAO;IAIlC,yBAAyB,IAAI,OAAO;IAIpC,mBAAmB,IAAI,OAAO;IAIxB,kBAAkB,CAC/B,OAAO,GAAE,kBAAuB,GAC/B,OAAO,CAAC,WAAW,CAAC;IAwCR,cAAc,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC;IAIhE,MAAM,IAAI,SAAS;IAInB,OAAO,IAAI,OAAO;IAIlB,cAAc,IAAI,cAAc;IA4BhC,SAAS,IAAI,QAAQ;IAI9B,IAAa,QAAQ,IAAI,WAAW,CAEnC;IAED,IAAa,WAAW,IAAI,cAAc,CAEzC;IAED,IAAa,QAAQ,IAAI,QAAQ,CAEhC;IAED,IAAa,OAAO,IAAI,OAAO,CAE9B;IAEQ,MAAM,IAAI,KAAK,EAAE;IAIjB,OAAO,IAAI,YAAY,EAAE;IAInB,sBAAsB,CAAC,KAAK,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAMrD,sBAAsB,CAAC,MAAM,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAQtD,mBAAmB,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAOpD,cAAc,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAI/C,wBAAwB,CACrC,iBAAiB,EAAE,iBAAiB,GAAG,IAAI,GAC1C,OAAO,CAAC,IAAI,CAAC;IAMP,2BAA2B,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAIlD,iBAAiB,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAIxC,iBAAiB,IAAI,MAAM;IAI3B,2BAA2B,IAAI,MAAM;IAI/B,YAAY,CAAC,SAAS,EACnC,eAAe,EAAE,QAAQ,CAAC,SAAS,CAAC,GACnC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;IAiBlB,OAAO,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IA4B7C,YAAY,CACzB,GAAG,OAAO,EAAE,oBAAoB,EAAE,GACjC,OAAO,CAAC,IAAI,CAAC;IA2BD,SAAS,CAAC,GAAG,OAAO,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAiCnD,cAAc,CAC3B,IAAI,EAAE,MAAM,EAEZ,YAAY,EAAE,QAAQ,GAAG;QAAC,OAAO,EAAE,QAAQ,CAAA;KAAC,GAC3C,OAAO,CAAC,IAAI,CAAC;IAgCD,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAelD,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAI5D,mBAAmB,CAChC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAC9B,OAAO,CAAC,IAAI,CAAC;IAID,YAAY,CACzB,SAAS,EAAE,MAAM,EACjB,iBAAiB,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,iBAAiB,GACvD,OAAO,CAAC,IAAI,CAAC;IAOD,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC;IA8H3B,MAAM,CACnB,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAYhB,gBAAgB,IAAI,OAAO,CAAC,UAAU,CAAC;IAIvC,MAAM,CACnB,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAIhB,SAAS,CACtB,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAwBhB,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC;IAI7B,oBAAoB,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAIrD,YAAY,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAI7C,gBAAgB,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAI9C,oBAAoB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAI1D,oBAAoB,CACjC,QAAQ,CAAC,EAAE,YAAY,EAAE,GACxB,OAAO,CAAC,IAAI,CAAC;IAID,eAAe,CAAC,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAInD,gBAAgB,CAAC,SAAS,CAAC,EAAE;QAC1C,YAAY,EAAE,OAAO,CAAC;QACtB,gBAAgB,EAAE,OAAO,CAAC;KAC3B,GAAG,OAAO,CAAC,IAAI,CAAC;IAIF,uBAAuB,CACpC,IAAI,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,kCAAkC,CAAC,MAAM,CAAC,GACnE,OAAO,CAAC,IAAI,CAAC;IAID,WAAW,CAAC,QAAQ,EAAE,QAAQ,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAQ3D,QAAQ,IAAI,QAAQ,GAAG,IAAI;IAIrB,qBAAqB,CAClC,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,CAAC,GAAG,IAAI,EAAE,MAAM,KAAK,OAAO,GAAG,CAAC,GAAG,IAAI,EAAE,MAAM,KAAK,OAAO,EAExE,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,2BAA2B,CAAC;IAKxB,mCAAmC,CAChD,UAAU,EAAE,MAAM,GACjB,OAAO,CAAC,IAAI,CAAC;IAMD,eAAe,CAAC,OAAO,UAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAI9C,WAAW,CACxB,OAAO,EAAE,QAAQ,CAAC,iBAAiB,CAAC,GACnC,OAAO,CAAC,MAAM,CAAC;IAmDH,eAAe,CAC5B,OAAO,GAAE,UAAe,GACvB,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IA0EvB,GAAG,CAAC,OAAO,GAAE,UAAe,GAAG,OAAO,CAAC,UAAU,CAAC;IAQlD,KAAK,CAClB,OAAO,GAAE;QAAC,eAAe,CAAC,EAAE,OAAO,CAAA;KAAgC,GAClE,OAAO,CAAC,IAAI,CAAC;IAkBP,QAAQ,IAAI,OAAO;IAI5B,IAAa,KAAK,IAAI,QAAQ,CAE7B;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACY,mBAAmB,CAChC,OAAO,GAAE,kBAAuB,GAC/B,OAAO,CAAC,mBAAmB,CAAC;CAGhC;AAwCD,wBAAgB,4CAA4C,CAC1D,YAAY,EAAE,kBAAkB,GAAG,MAAM,GAAG,SAAS,GACpD,QAAQ,CAAC,OAAO,CAAC,kBAAkB,GAAG,SAAS,CAcjD"}