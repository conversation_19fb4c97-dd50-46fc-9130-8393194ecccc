{"version": 3, "file": "HandleIterator.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/HandleIterator.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,oBAAoB,CAAC;AAGjD,OAAO,KAAK,EAAC,iBAAiB,EAAE,SAAS,EAAC,MAAM,YAAY,CAAC;AAsD7D;;GAEG;AACH,wBAAuB,uBAAuB,CAAC,CAAC,EAC9C,MAAM,EAAE,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,GACrC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAOrC"}